import { httpClient } from './http'
import { APIRouter } from '@/services/apiRouter'
import type { Card, CardType } from '@/types'

// 卡密生成请求类型 (新API格式)
export interface CardGenerateRequest {
  packageType: string  // 新API使用packageType而不是type
  quantity?: number    // 新API使用quantity而不是count
  customCode?: string  // 新API使用customCode而不是code
  // 保持向后兼容
  type?: string
  count?: number
  preview?: boolean
  code?: string
}

// 新API卡密响应类型
export interface CardGenerateResponse {
  success: boolean
  generated: number
  requested: number
  cards: Array<{
    code: string
    packageType: string
    packageInfo: {
      type: string
      duration: number
      quotaChars: number
      price: number
      description: string
    }
  }>
  error?: string
  errors?: string[]
  // 兼容性字段
  code?: string              // 单个卡密代码（兼容旧API）
  data?: {                   // 嵌套数据结构
    code?: string
    cards?: Array<{
      code: string
    }>
  }
}

// 卡密编辑请求类型
export interface CardEditRequest {
  oldCode: string
  newCode: string
  type: string
}

// 卡密API服务
export class CardService {
  // 卡密类型配置 (基于API文档标准)
  static readonly CARD_TYPES: CardType[] = [
    { value: 'M', label: '标准月套餐', description: '30天 | ¥25 | 80,000字符' },
    { value: 'Q', label: '标准季度套餐', description: '90天 | ¥55 | 250,000字符' },
    { value: 'H', label: '标准半年套餐', description: '180天 | ¥99 | 550,000字符' },
    { value: 'PM', label: 'PRO月套餐', description: '30天 | ¥45 | 250,000字符' },
    { value: 'PQ', label: 'PRO季度套餐', description: '90天 | ¥120 | 800,000字符' },
    { value: 'PH', label: 'PRO半年套餐', description: '180天 | ¥220 | 2,000,000字符' },
    { value: 'PT', label: '测试套餐', description: '30分钟 | ¥0 | 2,000字符' }
  ]

  // 获取卡密类型名称
  static getCardTypeName(type: string): string {
    const cardType = this.CARD_TYPES.find(t => t.value === type)
    return cardType ? cardType.label : type
  }

  // 获取可用套餐类型 (使用API路由器)
  static async getPackageTypes(): Promise<CardType[]> {
    try {
      const endpoint = APIRouter.getEndpoint('ADMIN_CARDS_PACKAGES')
      APIRouter.logAPICall('ADMIN_CARDS_PACKAGES', endpoint)
      const response = await httpClient.get<any>(endpoint)

      if (response.success || (response as any).packages) {
        const packages = (response as any).packages || response.data?.packages || []
        // 转换为CardType格式
        return packages.map((pkg: any) => ({
          value: pkg.type,
          label: pkg.description || `${pkg.type}套餐`,
          description: `${pkg.days}天 | ¥${pkg.price} | ${pkg.chars.toLocaleString()}字符`
        }))
      }

      throw new Error(response.message || response.error || '获取套餐类型失败')
    } catch (error) {
      // 如果新API失败，返回静态配置作为备用
      console.warn('获取套餐类型API失败，使用静态配置:', error)
      return this.CARD_TYPES
    }
  }



  // 获取卡密列表 (使用API路由器)
  static async getCards(params?: {
    page?: number
    limit?: number
    status?: 'unused' | 'used'
    packageType?: string
  }): Promise<Card[]> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams()
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.status) queryParams.append('status', params.status)
      if (params?.packageType) queryParams.append('packageType', params.packageType)

      const endpoint = APIRouter.getEndpoint('ADMIN_CARDS')
      const url = `${endpoint}${queryParams.toString() ? '?' + queryParams.toString() : ''}`
      APIRouter.logAPICall('ADMIN_CARDS', url)
      const response = await httpClient.get<any>(url)

      if (response.success || (response as any).cards) {
        // 新API响应格式: { cards: [...], pagination: {...} }
        const rawCards = (response as any).cards || response.data?.cards || []

        // 标准化数据格式，支持新旧格式
        return rawCards.map((card: any) => {
          const parseTime = (timeValue: string | number | undefined): number => {
            if (!timeValue) return 0
            if (typeof timeValue === 'string') {
              return new Date(timeValue).getTime()
            }
            return timeValue
          }

          return {
            id: card.id,
            code: card.code,
            // 新格式字段
            package_type: card.package_type || card.packageType,
            package_info: card.package_info || card.packageInfo,
            created_at: card.created_at,
            used_at: card.used_at,
            used_by: card.used_by,
            userUsage: card.userUsage ? {
              totalChars: card.userUsage.totalChars || 0,
              monthlyChars: card.userUsage.monthlyChars || 0,
              monthlyResetAt: card.userUsage.monthlyResetAt || 0
            } : undefined,
            // 兼容旧格式
            type: card.package_type || card.packageType || card.type,
            status: card.status,
            usedBy: card.used_by || card.user || card.usedBy,
            activatedAt: parseTime(card.used_at || card.activatedAt),
            createdAt: parseTime(card.created_at || card.createdAt) || Date.now(),
            totalChars: card.userUsage?.totalChars || card.totalChars || 0
          } as Card
        })
      }

      throw new Error(response.message || response.error || '获取卡密列表失败')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '获取卡密列表失败')
    }
  }



  // 生成卡密预览 (使用API路由器)
  static async generatePreview(type: string): Promise<string> {
    try {
      // 根据当前API模式选择预览方式
      const endpoint = APIRouter.getEndpoint('ADMIN_CARDS_GENERATE')
      APIRouter.logAPICall('ADMIN_CARDS_GENERATE', endpoint)

      if (!endpoint) {
        // 兼容模式可能没有预览功能，返回示例代码
        return `${type.toUpperCase()}-PREVIEW-${Date.now().toString().slice(-6)}`
      }

      const response = await httpClient.post<{ code: string }>(endpoint, {
        packageType: type,
        quantity: 1,
        preview: true
      })

      if (response.success && (response.data?.code || (response as any).code)) {
        return response.data?.code || (response as any).code || ''
      }

      throw new Error(response.message || response.error || '生成预览失败')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '生成预览失败')
    }
  }

  // 生成卡密 (使用API路由器)
  static async generateCard(request: CardGenerateRequest): Promise<string> {
    try {
      // 构建API请求参数
      const apiRequest = {
        packageType: request.packageType || request.type || 'PT',
        quantity: 1, // 单个生成
        ...(request.customCode || request.code ? { customCode: request.customCode || request.code } : {})
      }

      const endpoint = APIRouter.getEndpoint('ADMIN_CARDS_GENERATE')
      APIRouter.logAPICall('ADMIN_CARDS_GENERATE', endpoint)
      const response = await httpClient.post<CardGenerateResponse>(endpoint, apiRequest)

      // 处理新API格式响应（包含cards数组）
      if (response.success && (response as any).cards && (response as any).cards.length > 0) {
        return (response as any).cards[0].code
      }

      // 处理旧API格式响应（直接返回code）
      if (response.success && ((response as any).code || response.data?.code)) {
        return (response as any).code || response.data?.code
      }

      // 如果API返回错误信息
      if ((response as any).error || (response as any).errors) {
        const errorMsg = (response as any).error || (response as any).errors?.[0] || '生成卡密失败'
        throw new Error(errorMsg)
      }

      throw new Error(response.message || response.error || '生成卡密失败')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '生成卡密失败')
    }
  }

  // 批量生成卡密 (使用新管理员API的批量功能)
  static async generateCards(type: string, count: number): Promise<string[]> {
    try {
      // 使用新API的批量生成功能
      const newApiRequest = {
        packageType: type,
        quantity: count
      }

      const endpoint = APIRouter.getEndpoint('ADMIN_CARDS_GENERATE')
      APIRouter.logAPICall('ADMIN_CARDS_GENERATE', endpoint)
      const response = await httpClient.post<CardGenerateResponse>(endpoint, newApiRequest)

      // 处理新API格式响应（包含cards数组）
      if (response.success && (response as any).cards) {
        return (response as any).cards.map((card: any) => card.code)
      }

      // 处理旧API格式响应（单个卡密）
      if (response.success && ((response as any).code || response.data?.code)) {
        return [(response as any).code || response.data?.code]
      }

      // 如果新API返回部分成功
      if ((response as any).generated > 0 && (response as any).cards) {
        const successCards = (response as any).cards.map((card: any) => card.code)
        const errors = (response as any).errors || []
        console.warn(`批量生成部分成功: ${(response as any).generated}/${(response as any).requested}`, errors)
        return successCards
      }

      throw new Error((response as any).error || response.message || response.error || '批量生成卡密失败')
    } catch (error) {
      // 如果新API失败，回退到逐个生成的方式
      console.warn('新批量生成API失败，回退到逐个生成:', error)
      const cards: string[] = []

      for (let i = 0; i < count; i++) {
        try {
          const code = await this.generateCard({ packageType: type, type })
          cards.push(code)
        } catch (generateError) {
          console.error(`生成第${i + 1}个卡密失败:`, generateError)
          throw new Error(`生成第${i + 1}个卡密失败: ${generateError instanceof Error ? generateError.message : '未知错误'}`)
        }
      }

      return cards
    }
  }

  // 编辑卡密
  static async editCard(request: CardEditRequest): Promise<void> {
    try {
      const endpoint = APIRouter.getEndpoint('CARD_EDIT')
      APIRouter.logAPICall('CARD_EDIT', endpoint)
      const response = await httpClient.post(endpoint, request)
      
      if (!response.success) {
        throw new Error(response.message || '编辑卡密失败')
      }
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '编辑卡密失败')
    }
  }

  // 删除卡密
  static async deleteCard(code: string): Promise<void> {
    try {
      const endpoint = APIRouter.getEndpoint('CARD_DELETE')
      APIRouter.logAPICall('CARD_DELETE', endpoint)
      const response = await httpClient.post(endpoint, { code })
      
      if (!response.success) {
        throw new Error(response.message || '删除卡密失败')
      }
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '删除卡密失败')
    }
  }
}
